import {
  Form,
  Select,
  Slider,
  InputNumber,
  Input,
  Button,
  Upload,
  Image,
  type GetProp,
  App,
} from 'antd';
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Sender, Prompts } from '@ant-design/x';
import { CloudUploadOutlined, DeleteOutlined } from '@ant-design/icons';
import api from '@/api';
import { type AgentMessage } from './components/ChartView';
import { ModelList } from './type';

import { v4 as uuidv4 } from 'uuid';

import {
  getModelInfoListForSelect,
  getKnowledgeBaseListForSelect,
  uploadImagesForDeepPrompt,
  createSession,
  deleteSession,
} from './api';
import { useAuthStore } from '@/store/features';

import { ChatView, ChatViewProps } from './components';
import { promptsItems } from './constants';

export interface ModelConfig {
  modelName: string;
  modelConfigId: number;
  baseUuid: string;
  modelPath: string;
  taskUuid_base: string;
  taskUuid: string;
  modelType?: string; // 添加模型类型字段
  systemPrompt?: string;
  maxTokens: number;
  temperature: number;
  topP: number;
  topK: number;
}

interface FormFields {
  models: ModelConfig[];
}

interface ModelFieldsProps {
  field: any;
  form: any;
  clearHistory: () => void;
  resetFormToDefaults: () => void;
}

const ModelFields = ({
  field,
  form,
  clearHistory,
  resetFormToDefaults,
}: ModelFieldsProps) => {
  const index = field.name;
  const { message } = App.useApp();

  const updateField = (
    key: keyof ModelConfig,
    value: number | string | undefined | null,
  ) => {
    const models = form.getFieldValue('models') || [];
    models[index] = { ...models[index], [key]: value };
    form.setFieldsValue({ models });
  };

  const models = form.getFieldValue('models') || [];

  const [modelList, setModelList] = useState<ModelList[]>([]);
  const [knowledgeList, setKnowledgeList] = useState<any[]>([]);

  const [modeTypeList, setModeTypeList] = useState<any[]>([]);

  const getModeTypeListFetch = async () => {
    try {
      const res = await api.getModelTypeListForChat();
      if (!res.code) {
        setModeTypeList(
          res.data.list.map((item: string) => ({ label: item, value: item })),
        );
      } else {
        message.error(res.msg);
      }
    } catch (error) {
      console.error('Error fetching model types:', error);
    }
  };

  const getModelInfoListForSelectFetch = async (type: string) => {
    try {
      const res = await getModelInfoListForSelect(type);
      if (!res.code) {
        setModelList(res.data.list as any);
      } else {
        message.error(res.msg);
      }
    } catch (error) {
      console.error('Error fetching model config list:', error);
    }
  };

  const getKnowledgeBaseListForSelectFetch = async () => {
    try {
      const res = await getKnowledgeBaseListForSelect();
      if (!res.code) {
        setKnowledgeList(res.data.list);
      } else {
        message.error(res.msg);
      }
    } catch (error) {
      console.error('Error fetching model config list:', error);
    }
  };

  useEffect(() => {
    // getModelInfoListForSelectFetch();
    getKnowledgeBaseListForSelectFetch();
    getModeTypeListFetch();
  }, []);

  useEffect(() => {
    if (models[index]['modelType'])
      getModelInfoListForSelectFetch(models[index]['modelType']);
  }, [models[index]['modelType']]);

  return (
    <div
      key={field.key}
      className='mb-6 border p-4 rounded-lg border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200 bg-white'>
      <Form.Item
        label={
          <span className='text-gray-700 font-medium'>{t('模型类型')}</span>
        }
        name={[index, 'modelType']}>
        <Select
          options={modeTypeList}
          onChange={(value: string) => {
            getModelInfoListForSelectFetch(value);

            // 1. 清除对话历史
            clearHistory();

            // 2. 重置左侧表单到默认值
            resetFormToDefaults();

            // 3. 更新所有Form.List项的ModelType以保持一致性
            const models = form.getFieldValue('models') || [];
            const updatedModels = models.map((model: ModelConfig) => ({
              ...model,
              modelType: value,
              // 重置模型选择相关字段
              taskUuid_base: undefined,
              modelConfigId: undefined,
              modelName: undefined,
              modelPath: undefined,
              taskUuid: undefined,
              // 重置其他配置到默认值
              systemPrompt: t(
                '你是一个智能、可靠、礼貌且高效的 AI 助手，请你根据用户的要求提供清晰、有条理、准确的回答。',
              ),
              maxTokens: 2048,
              temperature: 0.7,
              topP: 0.7,
              topK: 5,
              baseUuid: undefined,
            }));

            form.setFieldsValue({ models: updatedModels });
          }}
        />
      </Form.Item>
      <Form.Item
        label={<span className='text-gray-700 font-medium'>Model</span>}
        name={[index, 'taskUuid_base']}
        rules={[
          {
            required: true,
            message: t('请选择模型'),
            validator: (_, value) => {
              if (!value) {
                return Promise.reject(t('请先选择模型才能进行对话'));
              }
              return Promise.resolve();
            },
          },
        ]}>
        <Select
          className='w-full'
          placeholder={t('选择模型')}
          options={modelList.map((item) => ({
            title: item.groupName,
            label: <span>{item.groupName}</span>,
            options: item.modelInfoList.map((li, index) => {
              return {
                label: li.modelName,
                value: item.groupName + '$' + index,
              };
            }),
          }))}
          onChange={(value) => {
            const [groupName, modelIndex] = value.split('$');
            const model = modelList.find(
              (item) => item.groupName === groupName,
            );
            if (model && model.modelInfoList[Number(modelIndex)]) {
              updateField(
                'modelConfigId',
                model.modelInfoList[Number(modelIndex)].modelConfigId,
              );
              updateField(
                'modelName',
                model.modelInfoList[Number(modelIndex)].modelName,
              );
              updateField(
                'modelPath',
                model.modelInfoList[Number(modelIndex)].modelPath,
              );
              updateField(
                'taskUuid',
                model.modelInfoList[Number(modelIndex)].taskUuid,
              );
              console.log(model.modelInfoList[Number(modelIndex)]);
            }
            clearHistory();
          }}
        />
      </Form.Item>
      <Form.Item
        label={<span className='text-gray-700 font-medium'>System Prompt</span>}
        name={[index, 'systemPrompt']}
        initialValue={t(
          '你是一个智能、可靠、礼貌且高效的 AI 助手，请你根据用户的要求提供清晰、有条理、准确的回答。',
        )}>
        <Input.TextArea rows={2} />
      </Form.Item>
      <div className='mb-6'>
        <div className='flex justify-between items-center mb-1'>
          <span className='text-gray-700 font-medium'>Max Tokens</span>
          <Form.Item name={[index, 'maxTokens']} noStyle initialValue={2048}>
            <InputNumber
              min={0}
              max={10000}
              onChange={(value) => updateField('maxTokens', value)}
            />
          </Form.Item>
        </div>
        <Form.Item name={[index, 'maxTokens']} noStyle>
          <Slider min={0} max={10000} />
        </Form.Item>
      </div>
      <div className='mb-6'>
        <div className='flex justify-between items-center mb-1'>
          <span className='text-gray-700 font-medium'>Temperature</span>
          <Form.Item name={[index, 'temperature']} noStyle initialValue={0.7}>
            <InputNumber
              min={0}
              max={2}
              step={0.1}
              onChange={(value) => updateField('temperature', value)}
            />
          </Form.Item>
        </div>
        <Form.Item name={[index, 'temperature']} noStyle>
          <Slider min={0} max={1} step={0.1} />
        </Form.Item>
      </div>
      <div className='mb-6'>
        <div className='flex justify-between items-center mb-1'>
          <span className='text-gray-700 font-medium'>Top P</span>
          <Form.Item name={[index, 'topP']} noStyle initialValue={0.7}>
            <InputNumber
              min={0}
              max={1}
              step={0.1}
              onChange={(value) => updateField('topP', value)}
            />
          </Form.Item>
        </div>
        <Form.Item name={[index, 'topP']} noStyle>
          <Slider min={0} max={1} step={0.1} />
        </Form.Item>
      </div>
      <div className='mb-6'>
        <div className='flex justify-between items-center mb-1'>
          <span className='text-gray-700 font-medium'>Top K</span>
          <Form.Item name={[index, 'topK']} noStyle initialValue={5}>
            <InputNumber
              min={0}
              max={100}
              onChange={(value) => updateField('topK', value)}
            />
          </Form.Item>
        </div>
        <Form.Item name={[index, 'topK']} noStyle>
          <Slider min={0} max={100} />
        </Form.Item>
      </div>
      <div>
        <Form.Item label={t('知识库')} name={[index, 'baseUuid']}>
          <Select
            options={knowledgeList.map((item) => ({
              label: item.baseName,
              value: item.baseUuid,
            }))}
            allowClear
          />
        </Form.Item>
      </div>
    </div>
  );
};

export default function PromptWords() {
  const { message } = App.useApp();
  const [form] = Form.useForm<FormFields>();
  const [inputMessage, setInputMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string>('');
  const [images, setImages] = useState<string[]>([]);
  const [uploading, setUploading] = useState(false);
  const [uploadedImages, setUploadedImages] = useState<
    Array<{
      url: string;
      localUrl: string; // 本地 blob URL
      name: string;
      uid: string;
      fileName: string;
      file: File; // 保存原始文件对象
    }>
  >([]);
  const [allImages, setAllImages] = useState<
    Array<{
      url: string;
      localUrl: string; // 本地 blob URL
      name: string;
      uid: string;
      fileName: string;
      file: File; // 保存原始文件对象
    }>
  >([]);

  const models = Form.useWatch('models', form) || [];
  const requestRefs = useRef<Array<(content: AgentMessage) => void>>([]);
  const cancelRefs = useRef<Array<() => void>>([]);
  const { token } = useAuthStore();

  const responsePromisesRef = useRef<Array<Promise<void>>>([]);
  const clearRefs = useRef<Array<() => void>>([]);

  const hasValidModel = useCallback(() => {
    if (models.length === 1) {
      return models[0]?.taskUuid_base;
    }
    return models.length === 2 && models.every((model) => model?.taskUuid_base);
  }, [models]);

  // 检查是否有vision-language类型的模型
  const hasVisionLanguageModel = useCallback(() => {
    return models.some((model) => model?.modelType === 'vision-language');
  }, [models]);

  // 监听模型类型变化，如果没有vision-language类型的模型，清空已上传的图片
  useEffect(() => {
    if (
      !hasVisionLanguageModel() &&
      (uploadedImages.length > 0 || images.length > 0)
    ) {
      // 释放所有本地 blob URL
      uploadedImages.forEach((img) => {
        if (img.localUrl) {
          URL.revokeObjectURL(img.localUrl);
        }
      });
      setImages([]);
      setUploadedImages([]);
    }
  }, [hasVisionLanguageModel, uploadedImages, images]);

  // 创建新会话
  const createNewSession = useCallback(async () => {
    try {
      const res = await createSession();
      if (res.code === 0 && res.data?.result) {
        setSessionId(res.data.result);
        return res.data.result;
      } else {
        throw new Error(res.msg || t('创建会话失败'));
      }
    } catch (error: any) {
      console.error(t('创建会话失败，使用备用方案:'), error);
      message.warning(t('会话创建失败，使用本地生成的会话ID'));
      // 降级使用 UUID 作为备用方案
      const fallbackSessionId = uuidv4();
      setSessionId(fallbackSessionId);
      return fallbackSessionId;
    }
  }, []);

  // 获取有效的图片URL
  const getValidImageUrl = useCallback((image: any) => {
    // 优先使用本地URL，如果不存在或无效则使用服务器URL
    if (image.localUrl) {
      try {
        // 检查本地URL是否仍然有效
        const url = new URL(image.localUrl);
        if (url.protocol === 'blob:') {
          return image.localUrl;
        }
      } catch (e) {
        console.warn(t('本地URL无效:'), image.localUrl);
      }
    }

    // 使用服务器URL
    if (image.url) {
      return image.url;
    }

    // 如果都没有，返回空字符串
    console.warn(t('图片没有有效的URL:'), image);
    return '';
  }, []);

  // 图片上传处理函数
  const uploadImages = useCallback(
    async (files: File[]) => {
      if (!sessionId) {
        message.error('会话ID未初始化，请刷新页面重试');
        return;
      }

      // 先创建本地预览图片
      const localImages = files.map((file, index) => ({
        url: '', // 服务器URL稍后填充
        localUrl: URL.createObjectURL(file), // 创建本地 blob URL
        name: file.name,
        uid: `local-${Date.now()}-${index}`,
        fileName: '', // 服务器文件名稍后填充
        file: file, // 保存原始文件对象
      }));

      // 立即显示本地预览
      setUploadedImages((prev) => [...prev, ...localImages]);

      setUploading(true);
      try {
        const res = await uploadImagesForDeepPrompt(sessionId, files);
        if (res.code === 0) {
          // 根据实际响应结构处理数据：res.data.list 是文件名数组
          const uploadedFileNames = res.data.list || [];

          // 更新本地图片信息，添加服务器返回的数据
          setUploadedImages((prev) => {
            const updatedImages = [...prev];
            localImages.forEach((localImg, index) => {
              const imgIndex = updatedImages.findIndex(
                (img) => img.uid === localImg.uid,
              );
              if (imgIndex !== -1 && uploadedFileNames[index]) {
                updatedImages[imgIndex] = {
                  ...updatedImages[imgIndex],
                  url: `/api/file/image/${uploadedFileNames[index]}`,
                  fileName: uploadedFileNames[index],
                };
              }
            });
            return updatedImages;
          });

          setImages((prev) => [...prev, ...uploadedFileNames]);
          message.success(`成功上传 ${files.length} 张图片`);
        } else {
          // 上传失败，移除本地预览
          setUploadedImages((prev) =>
            prev.filter(
              (img) => !localImages.some((local) => local.uid === img.uid),
            ),
          );
          // 释放本地 blob URL
          localImages.forEach((img) => URL.revokeObjectURL(img.localUrl));
          throw new Error(res.msg || '上传失败');
        }
      } catch (error: any) {
        console.error('图片上传失败:', error);
        message.error(`图片上传失败: ${error.message}`);
        // 上传失败，移除本地预览并释放内存
        setUploadedImages((prev) =>
          prev.filter(
            (img) => !localImages.some((local) => local.uid === img.uid),
          ),
        );
        localImages.forEach((img) => URL.revokeObjectURL(img.localUrl));
      } finally {
        setUploading(false);
      }
    },
    [sessionId],
  );

  useEffect(() => {
    setAllImages((prev) => {
      // 将新上传的图片与现有图片合并
      const newImages = [...prev];
      uploadedImages.forEach((img) => {
        // 检查是否已存在相同uid的图片
        if (!newImages.some((existingImg) => existingImg.uid === img.uid)) {
          newImages.push(img);
        }
      });
      return newImages;
    });
  }, [images]);

  // 删除单张图片
  const removeImage = useCallback(
    (uid: string) => {
      // 找到要删除的图片
      const imageToRemove = uploadedImages.find((img) => img.uid === uid);

      // 释放本地 blob URL
      if (imageToRemove?.localUrl) {
        URL.revokeObjectURL(imageToRemove.localUrl);
      }

      setUploadedImages((prev) => prev.filter((img) => img.uid !== uid));
      setImages((prev) => {
        if (imageToRemove?.fileName) {
          return prev.filter(
            (fileName: string) => fileName !== imageToRemove.fileName,
          );
        }
        return prev;
      });
    },
    [uploadedImages],
  );

  const requestChat = useCallback(
    (
      modelState: ChatViewProps['modelState'],
      messages: any,
      requestSessionId?: string,
      requestImages?: string[],
    ) => {
      const abortController = new AbortController();
      // 优先使用传入的 sessionId 和 images，如果没有则使用组件状态中的
      const finalSessionId = requestSessionId || sessionId;
      const finalImages = requestImages || images;

      return {
        request: fetch('/api/knowledge/queryByKnowledgeBase', {
          method: 'POST',
          headers: {
            'X-Token': token,
          } as any,
          body: JSON.stringify({
            ...modelState,
            isStream: true,
            messages: messages,
            sessionId: finalSessionId,
            images:
              finalImages.length > 0
                ? finalImages.map((item) => ({
                    data: item,
                  }))
                : undefined,
          }),
          signal: abortController.signal,
        }),
        abort: abortController,
      };
    },
    [token, sessionId, images],
  );

  // 组件初始化时创建会话
  useEffect(() => {
    createNewSession();
  }, [createNewSession]);

  // 清空上传的图片列表
  const clearUploadedImages = useCallback(() => {
    // 释放所有本地 blob URL
    // uploadedImages.forEach((img) => {
    // if (img.localUrl) {
    //   URL.revokeObjectURL(img.localUrl);
    // }
    // });

    // 清空状态
    setImages([]);
    setUploadedImages([]);
  }, [uploadedImages]);

  // 组件卸载时清理所有 blob URL
  useEffect(() => {
    return () => {
      uploadedImages.forEach((img) => {
        if (img.localUrl) {
          URL.revokeObjectURL(img.localUrl);
        }
      });
    };
  }, []);

  // 重置表单到默认值的函数
  const resetFormToDefaults = useCallback(() => {
    const currentModels = form.getFieldValue('models') || [];
    const resetModels = currentModels.map((model: ModelConfig) => ({
      modelType: model.modelType, // 保持当前的modelType
      systemPrompt: t(
        '你是一个智能、可靠、礼貌且高效的 AI 助手，请你根据用户的要求提供清晰、有条理、准确的回答。',
      ),
      maxTokens: 2048,
      temperature: 0.7,
      topP: 0.7,
      topK: 5,
      // 清空其他字段
      taskUuid_base: undefined,
      modelConfigId: undefined,
      modelName: undefined,
      modelPath: undefined,
      taskUuid: undefined,
      baseUuid: undefined,
    }));

    form.setFieldsValue({ models: resetModels });
  }, [form]);

  const clearHistory = async () => {
    try {
      // 如果有当前会话ID，先删除会话
      if (sessionId) {
        await deleteSession(sessionId);
      }
    } catch (error: any) {
      console.error(t('删除会话失败:'), error);
      // 删除会话失败不影响清除操作，继续执行
      message.warning(t('删除会话失败，但将继续清除本地数据'));
    }

    // 释放所有本地 blob URL
    uploadedImages.forEach((img) => {
      if (img.localUrl) {
        URL.revokeObjectURL(img.localUrl);
      }
    });

    // 创建新的 sessionId 并清空图片
    await createNewSession();
    setImages([]);
    setUploadedImages([]);

    clearRefs.current.forEach((clear) => {
      if (clear) clear();
    });
  };

  const onPromptsItemClick: GetProp<typeof Prompts, 'onItemClick'> = (info) => {
    if (!hasValidModel()) return;
    let completedCount = 0;
    const totalRequests = requestRefs.current.filter(Boolean).length;
    setLoading(true);

    requestRefs.current.forEach((request) => {
      if (request) {
        request({
          content: info.data.description as string,
          role: 'user',
          sessionId,
          images:
            images.length > 0
              ? images.map((item) => ({
                  data: item,
                }))
              : undefined,
          onComplete: () => {
            completedCount++;
            if (completedCount === totalRequests) {
              setLoading(false);
            }
          },
        } as AgentMessage);
      }
    });

    // 发送消息后立即清空上传的图片列表
    clearUploadedImages();
  };

  return (
    <div className='w-full h-full bg-gray-50 flex'>
      <div
        className='w-1/4 h-full overflow-auto px-4 py-6 border-r border-gray-200 
        [&::-webkit-scrollbar]:w-2 
        [&::-webkit-scrollbar-thumb]:bg-gray-300 
        [&::-webkit-scrollbar-thumb]:rounded-full
        [&::-webkit-scrollbar-track]:bg-transparent'>
        <Form className='space-y-4' form={form} layout='vertical'>
          <Form.Item name='models' initialValue={[{}]}>
            <Form.List name='models'>
              {(fields, { add }) => (
                <>
                  {fields.map((field) => (
                    <ModelFields
                      key={field.key}
                      field={field}
                      form={form}
                      clearHistory={clearHistory}
                      resetFormToDefaults={resetFormToDefaults}
                    />
                  ))}

                  {fields.length < 2 ? (
                    <Form.Item>
                      <Button
                        type='dashed'
                        onClick={add}
                        block
                        className='hover:border-blue-500 hover:text-blue-500'>
                        {t('添加模型')}
                      </Button>
                    </Form.Item>
                  ) : (
                    <div className='flex justify-around gap-4 mb-4'>
                      <Button
                        type='dashed'
                        danger
                        className='flex-1 hover:opacity-80'
                        onClick={() => {
                          const currentModels = form.getFieldValue('models');
                          currentModels.splice(1, 1);
                          form.setFieldsValue({ models: currentModels });
                          requestRefs.current.splice(1, 1);
                        }}>
                        {t('取消对比')}
                      </Button>
                      <Button
                        type='dashed'
                        className='flex-1 hover:border-blue-500 hover:text-blue-500'
                        onClick={() => {
                          const primary = form.getFieldValue('models')[0];
                          const currentModels = form.getFieldValue('models');
                          currentModels[1] = { ...primary };
                          form.setFieldsValue({ models: currentModels });
                        }}>
                        {t('同步参数')}
                      </Button>
                    </div>
                  )}
                </>
              )}
            </Form.List>
          </Form.Item>
        </Form>
      </div>
      <div className='flex-1 h-full flex flex-col p-6'>
        <div className='flex-1 h-full flex flex-col rounded-lg bg-white shadow-sm'>
          <div className='no-scrollbar full overflow-y-auto gap-2 p-4 box-border text-sm flex-1 flex flex-row'>
            {models.map((item, index: number) => {
              const model = item?.modelPath;
              const key = model + index || index;
              const currentModel = form.getFieldValue('models')[index] || {};

              return (
                <div className='w-full h-full' key={key}>
                  <ChatView
                    key={key}
                    modelState={{
                      modelName: currentModel.modelName,
                      modelConfigId: currentModel.modelConfigId,
                      baseUuid: currentModel.baseUuid,
                      modelPath: currentModel.modelPath,
                      taskUuid: currentModel.taskUuid,
                      systemPrompt: currentModel.systemPrompt ?? '',
                      maxTokens: currentModel.maxTokens,
                      temperature: currentModel.temperature,
                      topP: currentModel.topP,
                      topK: currentModel.topK,
                    }}
                    uploadedImages={allImages}
                    requestChat={requestChat}
                    onRequestRef={(ref) => {
                      requestRefs.current[index] = ref;
                    }}
                    onCancelRef={(ref) => {
                      cancelRefs.current[index] = ref;
                    }}
                    onClearRef={(ref) => {
                      clearRefs.current[index] = ref;
                    }}
                  />
                </div>
              );
            })}
          </div>
          <div className='w-full bg-gray-50 rounded-lg p-4 transition-all duration-300 flex flex-col border-t border-gray-200'>
            <div className='flex justify-between mb-3 gap-2'>
              <Prompts items={promptsItems} onItemClick={onPromptsItemClick} />
              <Button
                type='text'
                danger
                className='hover:bg-red-50'
                onClick={clearHistory}
                disabled={!hasValidModel()}>
                {t('清除对话')}
              </Button>
            </div>

            {/* 图片上传区域 - 只有当模型类型为vision-language时才显示 */}
            {hasVisionLanguageModel() && uploadedImages.length > 0 && (
              <div className='mb-3 p-3 bg-white rounded-lg border border-gray-200'>
                <div className='flex flex-wrap gap-2'>
                  {uploadedImages.map((image) => (
                    <div key={image.uid} className='relative group'>
                      <Image
                        src={getValidImageUrl(image)}
                        alt={image.name}
                        width={64}
                        height={64}
                        className='object-cover rounded-lg border border-gray-200'
                        style={{ width: '64px', height: '64px' }}
                        preview={{
                          src: getValidImageUrl(image),
                          mask: t('预览'),
                        }}
                        // fallback={getValidImageUrl(image)}
                      />
                      <button
                        onClick={() => removeImage(image.uid)}
                        className='absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity z-10'>
                        <DeleteOutlined />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 图片上传组件 - 只有当模型类型为vision-language时才显示 */}
            {hasVisionLanguageModel() && (
              <div className='mb-3'>
                <Upload
                  accept='.jpg,.jpeg,.png'
                  multiple
                  showUploadList={false}
                  beforeUpload={(file) => {
                    const validTypes = ['image/jpeg', 'image/jpg', 'image/png'];
                    if (!validTypes.includes(file.type)) {
                      message.error(
                        '仅支持上传 JPG、JPEG 或 PNG 格式的图片文件',
                      );
                      return false;
                    }

                    const isLt5M = file.size / 1024 / 1024 < 5;
                    if (!isLt5M) {
                      message.error('图片大小不能超过 5MB');
                      return false;
                    }

                    return true;
                  }}
                  customRequest={({ file, onSuccess }) => {
                    uploadImages([file as File]).then(() => {
                      onSuccess?.('ok');
                    });
                  }}
                  disabled={uploading || !hasValidModel()}>
                  <Button
                    icon={<CloudUploadOutlined />}
                    loading={uploading}
                    disabled={uploading || !hasValidModel()}
                    className='w-full'>
                    {uploading ? t('上传中...') : t('上传图片 (JPG/PNG)')}
                  </Button>
                </Upload>
              </div>
            )}

            <Sender
              value={inputMessage}
              onChange={setInputMessage}
              loading={loading}
              onSubmit={() => {
                if (!inputMessage.trim()) return;
                setInputMessage('');
                setLoading(true);

                let completedCount = 0;
                const totalRequests =
                  requestRefs.current.filter(Boolean).length;

                requestRefs.current.forEach((request) => {
                  if (request) {
                    request({
                      content: inputMessage,
                      role: 'user',
                      sessionId,
                      images:
                        images.length > 0
                          ? images.map((item) => ({
                              data: item,
                            }))
                          : undefined,
                      onComplete: () => {
                        completedCount++;
                        if (completedCount === totalRequests) {
                          setLoading(false);
                        }
                      },
                    } as AgentMessage);
                  }
                });

                // 发送消息后立即清空上传的图片列表
                clearUploadedImages();
              }}
              onCancel={() => {
                cancelRefs.current.forEach((cancel) => {
                  if (cancel) {
                    cancel();
                  }
                });
                setLoading(false);
                responsePromisesRef.current = [];
              }}
              placeholder={
                models.length === 2 && !hasValidModel()
                  ? t('请确保两个模型都已选择...')
                  : t('请先选择模型...')
              }
              disabled={!hasValidModel()}
            />
          </div>
          <div className='text-xs text-gray-500 text-center py-4'>
            {t('内容由AI生成，无法确保真实准确，仅供参考，并请遵守本平台')}
            <a
              className='text-blue-500 hover:underline underline-offset-2 mx-1'
              href=''>
              {t('《用户协议》')}
            </a>
            {t('及国家网络信息安全相关规定')}
          </div>
        </div>
      </div>
    </div>
  );
}
