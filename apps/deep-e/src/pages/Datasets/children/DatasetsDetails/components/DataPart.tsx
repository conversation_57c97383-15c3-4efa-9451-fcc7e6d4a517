import React, { useEffect, useState, useRef } from 'react';
import { Table, Modal, Card, Tag, Button } from 'antd';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { getDatasetDetail, getDatasetSingleOriginalImage } from '../../../api';
import { DatasetDetailItem } from '../../../api/model';
import { t } from '@/languages';
import { COCOAnnotation, COCOCategory, COCOImage } from './AnnotatedImage';

// Multimodal data structure types
interface MultimodalAnnotation {
  id: string;
  label: string;
  bboxes?: number[][];
  attributes?: Record<string, any>;
  segmentations?: number[][];
}

interface MultimodalAnnotationGroup {
  label: string;
  annotations: MultimodalAnnotation[];
}

interface MultimodalImageData {
  imageId: string;
  annotationGroups: MultimodalAnnotationGroup[];
}

interface MultimodalQAItem {
  question: string;
  answer: string;
  lang: string;
  type: string;
}

interface MultimodalDatasetDetailItem {
  qa: MultimodalQAItem[];
  // 动态图片字段，支持任意数量的图片
  // images1, images2, images3, images4, etc. - Base64 encoded images
  // images1Name, images2Name, images3Name, images4Name, etc. - Annotation data
  [key: string]: any; // For dynamic image fields and other data
}

const calculateContentLength = (value: any): number => {
  if (typeof value === 'string') return value.length;
  if (typeof value === 'number') return String(value).length;
  return JSON.stringify(value).length;
};

type DataPartProps = {
  datasetUuid: string;
  datasetType: number;
  changTotalData: (total: number) => void;
};

const DataPart: React.FC<DataPartProps> = ({
  datasetUuid,
  changTotalData,
  datasetType,
}) => {
  const [dataList, setDataList] = useState<DatasetDetailItem[]>([]);
  const [multimodalData, setMultimodalData] = useState<
    MultimodalDatasetDetailItem[]
  >([]);
  const [columnNames, setColumnNames] = useState<string[]>([]);

  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // Modal相关状态
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedImageData, setSelectedImageData] = useState<{
    imageUrl: string;
    annotations: COCOAnnotation[];
    categories: COCOCategory[];
    imageInfo: COCOImage;
  } | null>(null);

  // 图片翻动相关状态
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [imageList, setImageList] = useState<
    Array<{
      columnName: string;
      imageId: string;
      imageUrl?: string; // 缓存的base64图片URL
      annotationData: MultimodalImageData;
    }>
  >([]);

  // 图片缓存（用于弹窗中的高清图片）
  const [imageCache, setImageCache] = useState<Record<string, string>>({});

  // 标注显示控制状态
  const [showBbox, setShowBbox] = useState(true);
  const [showSegmentations, setShowSegmentations] = useState(true);

  // 获取高清图片URL（用于弹窗显示）
  const getHighResImageUrl = async (imageId: string): Promise<string> => {
    // 先检查缓存
    if (imageCache[imageId]) {
      return imageCache[imageId];
    }

    try {
      const response = await getDatasetSingleOriginalImage({
        imageId,
        datasetUuid,
      });

      if (response.code === 0 && response.data.result) {
        const imageUrl = `data:image/jpeg;base64,${response.data.result}`;
        // 缓存图片
        setImageCache((prev) => ({
          ...prev,
          [imageId]: imageUrl,
        }));
        return imageUrl;
      }
    } catch (error) {
      console.error('获取高清图片失败:', error);
    }

    // 返回默认占位图片
    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9Ijc1IiB2aWV3Qm94PSIwIDAgMTAwIDc1IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iNzUiIGZpbGw9IiNmMGYwZjAiLz48dGV4dCB4PSI1MCIgeT0iNDAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZpbGw9IiM5OTkiPuWKoOi9veWkseaViTwvdGV4dD48L3N2Zz4=';
  };

  // 可缩放拖拽的图片组件
  const ZoomableImage: React.FC<{
    imageUrl: string;
    alt: string;
    annotations: COCOAnnotation[];
    categories: COCOCategory[];
    imageInfo: COCOImage;
    showBbox?: boolean;
    showSegmentations?: boolean;
  }> = ({
    imageUrl,
    alt,
    annotations,
    categories,
    imageInfo,
    showBbox = true,
    showSegmentations = true,
  }) => {
    const [scale, setScale] = useState(1);
    const [position, setPosition] = useState({ x: 0, y: 0 });
    const [isDragging, setIsDragging] = useState(false);
    const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
    const [imageLoaded, setImageLoaded] = useState(false);
    const [imageDimensions, setImageDimensions] = useState({
      width: 0,
      height: 0,
    });
    const currentImageUrlRef = useRef<string>('');
    const imageContainerRef = useRef<HTMLDivElement>(null);

    // 重置图片状态
    const resetImage = () => {
      setScale(1);
      setPosition({ x: 0, y: 0 });
    };

    // 处理鼠标按下
    const handleMouseDown = (event: React.MouseEvent) => {
      if (event.button === 2) {
        // 右键
        event.preventDefault();
        setIsDragging(true);
        setDragStart({
          x: event.clientX - position.x,
          y: event.clientY - position.y,
        });
      }
    };

    // 处理鼠标移动
    const handleMouseMove = (event: React.MouseEvent) => {
      if (isDragging) {
        setPosition({
          x: event.clientX - dragStart.x,
          y: event.clientY - dragStart.y,
        });
      }
    };

    // 处理鼠标抬起
    const handleMouseUp = () => {
      setIsDragging(false);
    };

    // 阻止右键菜单
    const handleContextMenu = (event: React.MouseEvent) => {
      event.preventDefault();
    };

    // 当图片URL改变时重置状态并计算初始缩放
    useEffect(() => {
      if (!imageUrl) return;

      currentImageUrlRef.current = imageUrl;
      setImageLoaded(false);

      const container = imageContainerRef.current;
      if (!container) return;

      const img = new Image();

      const handleLoad = () => {
        if (currentImageUrlRef.current !== imageUrl) return; // Stale image

        const imageWidth = img.naturalWidth;
        const imageHeight = img.naturalHeight;

        setImageDimensions({ width: imageWidth, height: imageHeight });
        setImageLoaded(true);

        // 计算并设置初始缩放以适应容器
        const canvasWidth = container.clientWidth;
        const canvasHeight = container.clientHeight;

        if (imageWidth > 0 && imageHeight > 0) {
          const scaleX = canvasWidth / imageWidth;
          const scaleY = canvasHeight / imageHeight;
          const initialScale = Math.min(scaleX, scaleY);

          // 只缩小大图，不放大已完整显示的小图
          setScale(initialScale < 1 ? initialScale : 1);
          setPosition({ x: 0, y: 0 });
        } else {
          // 图片尺寸无效时的回退
          resetImage();
        }
      };

      img.onload = handleLoad;
      img.onerror = () => {
        if (currentImageUrlRef.current === imageUrl) {
          setImageLoaded(false);
        }
      };

      // 开始加载前重置状态
      resetImage();
      img.src = imageUrl;

      // 处理已缓存的图片
      if (img.complete && img.naturalWidth > 0) {
        handleLoad();
      }
    }, [imageUrl]);

    // 使用useEffect处理滚轮事件以阻止Modal滚动
    useEffect(() => {
      const container = imageContainerRef.current;

      const handleWheel = (event: WheelEvent) => {
        if (event.ctrlKey) {
          // 仅在按下Ctrl时才阻止默认滚动行为
          event.preventDefault();
          event.stopPropagation();

          // 使用函数式更新以获取最新的 state
          setScale((prevScale) => {
            const delta = event.deltaY > 0 ? -0.1 : 0.1;
            return Math.max(0.1, Math.min(5, prevScale + delta));
          });
        }
        // 如果没有按下Ctrl键，则不执行任何操作，允许事件冒泡并滚动Modal
      };

      if (container) {
        // 关键：添加 passive: false 来确保 preventDefault 生效
        container.addEventListener('wheel', handleWheel, { passive: false });
      }

      return () => {
        if (container) {
          container.removeEventListener('wheel', handleWheel);
        }
      };
    }, []);

    // 处理图片加载完成（作为备用）
    const handleImageLoad = (event: React.SyntheticEvent<HTMLImageElement>) => {
      const img = event.currentTarget;

      // 检查是否还是当前的图片URL（避免竞态条件）
      if (currentImageUrlRef.current === img.src) {
        setImageDimensions({
          width: img.naturalWidth,
          height: img.naturalHeight,
        });
        setImageLoaded(true);
      }
    };

    return (
      <div
        ref={imageContainerRef}
        className='w-full h-full overflow-hidden relative flex items-center justify-center bg-gray-50'
        style={{
          cursor: isDragging ? 'grabbing' : 'grab',
        }}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onContextMenu={handleContextMenu}>
        {!imageLoaded && (
          <div className='text-gray-500 text-base'>{t('加载中')}...</div>
        )}

        {/* 高清图片加载提示 */}
        {imageLoaded &&
          imageUrl.includes('base64') &&
          imageUrl.length < 50000 && (
            <div className='absolute top-2 left-2 bg-blue-600 bg-opacity-90 text-white px-2 py-1 rounded text-xs'>
              {t('加载高清图片中')}...
            </div>
          )}

        {/* 图片容器 */}
        <div
          className='relative'
          style={{
            transform: `translate(${position.x}px, ${position.y}px) scale(${scale})`,
            transition: isDragging ? 'none' : 'transform 0.1s ease-out',
            display: imageLoaded ? 'block' : 'none',
          }}>
          <img
            src={imageUrl}
            alt={alt}
            className='max-w-none max-h-none select-none pointer-events-none block'
            onLoad={handleImageLoad}
            onError={() => setImageLoaded(true)}
          />

          {/* 标注覆盖层 */}
          {imageLoaded &&
            imageDimensions.width > 0 &&
            annotations.length > 0 && (
              <div className='absolute inset-0 pointer-events-none'>
                {/* SVG层用于绘制多边形 */}
                <svg
                  className='absolute inset-0 w-full h-full'
                  viewBox={`0 0 ${imageDimensions.width} ${imageDimensions.height}`}
                  style={{ pointerEvents: 'none' }}>
                  {annotations.map((annotation) => {
                    // 为不同的类别分配不同的颜色
                    const colors = [
                      '#ef4444', // red-500
                      '#3b82f6', // blue-500
                      '#10b981', // green-500
                      '#eab308', // yellow-500
                      '#8b5cf6', // purple-500
                      '#ec4899', // pink-500
                      '#6366f1', // indigo-500
                      '#f97316', // orange-500
                    ];
                    const color =
                      colors[annotation.category_id % colors.length];

                    return (
                      <g key={`svg-${annotation.id}`}>
                        {/* 绘制多边形（如果有segmentation数据且显示开关开启） */}
                        {showSegmentations &&
                          annotation.segmentation &&
                          annotation.segmentation.length > 0 &&
                          annotation.segmentation.map((segment, segIndex) => {
                            if (segment.length >= 6) {
                              // 至少需要3个点（6个坐标）才能构成多边形
                              const points = [];
                              for (let i = 0; i < segment.length; i += 2) {
                                const x =
                                  (segment[i] / imageInfo.width) *
                                  imageDimensions.width;
                                const y =
                                  (segment[i + 1] / imageInfo.height) *
                                  imageDimensions.height;
                                points.push(`${x},${y}`);
                              }
                              const pointsString = points.join(' ');

                              // 调试信息
                              console.log(
                                `绘制多边形 - ID: ${annotation.id}, 类别: ${annotation.category_id}, 顶点: ${pointsString}, 颜色: ${color}`,
                              );

                              return (
                                <polygon
                                  key={`polygon-${annotation.id}-${segIndex}`}
                                  points={pointsString}
                                  fill={`${color}80`} // 50% 透明度，更明显
                                  stroke={color}
                                  strokeWidth='4' // 更粗的边框
                                  strokeDasharray='none'
                                />
                              );
                            }
                            return null;
                          })}

                        {/* 绘制边界框（如果显示开关开启） */}
                        {showBbox &&
                          (() => {
                            const [x, y, width, height] = annotation.bbox;
                            const relativeX =
                              (x / imageInfo.width) * imageDimensions.width;
                            const relativeY =
                              (y / imageInfo.height) * imageDimensions.height;
                            const relativeWidth =
                              (width / imageInfo.width) * imageDimensions.width;
                            const relativeHeight =
                              (height / imageInfo.height) *
                              imageDimensions.height;

                            return (
                              <rect
                                x={relativeX}
                                y={relativeY}
                                width={relativeWidth}
                                height={relativeHeight}
                                fill='transparent'
                                stroke={color}
                                strokeWidth='2'
                              />
                            );
                          })()}
                      </g>
                    );
                  })}
                </svg>

                {/* HTML层用于绘制标签 */}
                {annotations.map((annotation, index) => {
                  const category = categories.find(
                    (c) => c.id === annotation.category_id,
                  );
                  const [x, y, width, height] = annotation.bbox;

                  // 计算相对于图片的位置和尺寸
                  const relativeX =
                    (x / imageInfo.width) * imageDimensions.width;
                  const relativeY =
                    (y / imageInfo.height) * imageDimensions.height;
                  const relativeWidth =
                    (width / imageInfo.width) * imageDimensions.width;
                  const relativeHeight =
                    (height / imageInfo.height) * imageDimensions.height;

                  // 为不同的类别分配不同的颜色
                  const colors = [
                    '#ef4444', // red-500
                    '#3b82f6', // blue-500
                    '#10b981', // green-500
                    '#eab308', // yellow-500
                    '#8b5cf6', // purple-500
                    '#ec4899', // pink-500
                    '#6366f1', // indigo-500
                    '#f97316', // orange-500
                  ];
                  const color = colors[annotation.category_id % colors.length];

                  return (
                    <div key={`label-${annotation.id}`}>
                      {/* 标签 */}
                      <div
                        className='absolute text-white px-1 py-0.5 text-xs rounded shadow-lg'
                        style={{
                          left: relativeX,
                          top: Math.max(0, relativeY - 22),
                          fontSize: '10px',
                          fontWeight: 'bold',
                          backgroundColor: color,
                        }}>
                        {category?.name || `ID:${annotation.category_id}`}
                      </div>

                      {/* 标注ID（可选，在右下角显示） */}
                      <div
                        className='absolute text-white px-1 rounded-full shadow-sm'
                        style={{
                          left: relativeX + relativeWidth - 20,
                          top: relativeY + relativeHeight - 15,
                          fontSize: '8px',
                          minWidth: '16px',
                          textAlign: 'center',
                          backgroundColor: color,
                        }}>
                        {index + 1}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
        </div>

        {/* 缩放控制提示 */}
        <div className='absolute bottom-2 right-2 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-xs pointer-events-none'>
          {Math.round(scale * 100)}% | Ctrl + 滚轮缩放 | 右键拖拽
        </div>
      </div>
    );
  };

  // 从多模态标注数据生成COCO格式标注数据
  const generateCOCODataFromMultimodalAnnotation = (
    imageUrl: string,
    annotationData: MultimodalImageData,
  ) => {
    const annotationGroups = annotationData.annotationGroups || [];

    // 从标注组中提取所有唯一的标签作为类别
    const uniqueLabels = new Set<string>();
    annotationGroups.forEach((group) => {
      group.annotations.forEach((ann) => {
        uniqueLabels.add(ann.label);
      });
    });

    const mockCategories: COCOCategory[] = Array.from(uniqueLabels).map(
      (label, index) => ({
        id: index + 1,
        name: label,
        supercategory:
          annotationGroups.find((group) =>
            group.annotations.some((ann) => ann.label === label),
          )?.label || 'object',
      }),
    );

    const mockImageInfo: COCOImage = {
      id: 1,
      file_name: annotationData.imageId || 'multimodal_image.jpg',
      width: 640,
      height: 480,
      url: imageUrl,
    };

    // 将多模态格式的标注转换为COCO格式
    const mockAnnotations: COCOAnnotation[] = [];
    let annotationId = 1;

    annotationGroups.forEach((group) => {
      group.annotations.forEach((annotation) => {
        const categoryId =
          mockCategories.findIndex((cat) => cat.name === annotation.label) + 1;

        // 处理有边界框的标注
        if (annotation.bboxes) {
          annotation.bboxes.forEach((bbox) => {
            // 假设bbox格式为 [x, y, width, height]
            const [x, y, width, height] = bbox;

            mockAnnotations.push({
              id: annotationId++,
              image_id: 1,
              category_id: categoryId,
              bbox: [x, y, width, height],
              area: width * height,
              iscrowd: 0,
              segmentation: annotation.segmentations || [
                [x, y, x + width, y, x + width, y + height, x, y + height],
              ],
            });
          });
        }
        // 处理只有分割数据而没有边界框的标注
        else if (
          annotation.segmentations &&
          annotation.segmentations.length > 0
        ) {
          annotation.segmentations.forEach((segmentation) => {
            if (segmentation.length >= 6) {
              // 从分割数据计算边界框
              const xCoords = [];
              const yCoords = [];
              for (let i = 0; i < segmentation.length; i += 2) {
                xCoords.push(segmentation[i]);
                yCoords.push(segmentation[i + 1]);
              }

              const minX = Math.min(...xCoords);
              const maxX = Math.max(...xCoords);
              const minY = Math.min(...yCoords);
              const maxY = Math.max(...yCoords);

              const width = maxX - minX;
              const height = maxY - minY;

              console.log(
                `处理只有分割数据的标注 - ID: ${annotation.id}, 标签: ${annotation.label}, 分割数据: [${segmentation}], 计算的边界框: [${minX}, ${minY}, ${width}, ${height}]`,
              );

              mockAnnotations.push({
                id: annotationId++,
                image_id: 1,
                category_id: categoryId,
                bbox: [minX, minY, width, height],
                area: width * height,
                iscrowd: 0,
                segmentation: [segmentation],
              });
            }
          });
        }
      });
    });

    return {
      imageUrl,
      annotations: mockAnnotations,
      categories: mockCategories,
      imageInfo: mockImageInfo,
    };
  };

  // 处理多模态图片点击事件
  const handleMultimodalImageClick = async (
    clickedImageUrl: string,
    clickedAnnotationData: MultimodalImageData,
    record: MultimodalDatasetDetailItem,
    event: React.MouseEvent,
  ) => {
    event.stopPropagation(); // 阻止事件冒泡，避免触发行点击

    // 收集当前行的所有图片信息
    const images: Array<{
      columnName: string;
      imageId: string;
      imageUrl?: string;
      annotationData: MultimodalImageData;
    }> = [];

    // 遍历columnNames，找到所有图片列
    columnNames.forEach((columnName) => {
      if (columnName.startsWith('images') && !columnName.endsWith('Name')) {
        const imageData = record[columnName];
        const annotationKey = `${columnName}Name`;
        const annotationData = record[annotationKey] as MultimodalImageData;

        if (imageData && typeof imageData === 'string') {
          const thumbnailUrl = `data:image/jpeg;base64,${imageData}`; // 表格中的缩略图
          images.push({
            columnName,
            imageId: annotationData?.imageId || `${columnName}_image`,
            imageUrl: thumbnailUrl, // 暂时使用缩略图，弹窗时会获取高清图
            annotationData: annotationData || {
              imageId: `${columnName}_image`,
              annotationGroups: [],
            },
          });
        }
      }
    });

    // 找到当前点击图片的索引
    const clickedIndex = images.findIndex(
      (img) => img.imageUrl === clickedImageUrl,
    );

    setImageList(images);
    setCurrentImageIndex(clickedIndex >= 0 ? clickedIndex : 0);

    // 设置当前图片的标注数据，先使用缩略图显示
    const currentImage = images[clickedIndex >= 0 ? clickedIndex : 0];
    if (currentImage) {
      // 先用缩略图显示，然后异步加载高清图
      const cocoData = generateCOCODataFromMultimodalAnnotation(
        currentImage.imageUrl || clickedImageUrl,
        currentImage.annotationData,
      );
      setSelectedImageData(cocoData);

      // 异步获取高清图片并更新
      getHighResImageUrl(currentImage.imageId).then((highResUrl) => {
        const updatedCocoData = generateCOCODataFromMultimodalAnnotation(
          highResUrl,
          currentImage.annotationData,
        );
        setSelectedImageData(updatedCocoData);
      });
    } else {
      // 如果没有找到图片，使用点击的图片数据
      const cocoData = generateCOCODataFromMultimodalAnnotation(
        clickedImageUrl,
        clickedAnnotationData || {
          imageId: 'unknown_image',
          annotationGroups: [],
        },
      );
      setSelectedImageData(cocoData);
    }

    setModalVisible(true);
  };

  // 切换到上一张图片
  const handlePreviousImage = async () => {
    if (imageList.length <= 1) return;

    const newIndex =
      currentImageIndex > 0 ? currentImageIndex - 1 : imageList.length - 1;
    setCurrentImageIndex(newIndex);

    const currentImage = imageList[newIndex];
    if (currentImage) {
      // 先用缩略图显示
      const cocoData = generateCOCODataFromMultimodalAnnotation(
        currentImage.imageUrl || '',
        currentImage.annotationData,
      );
      setSelectedImageData(cocoData);

      // 异步获取高清图片并更新
      try {
        const highResUrl = await getHighResImageUrl(currentImage.imageId);
        const updatedCocoData = generateCOCODataFromMultimodalAnnotation(
          highResUrl,
          currentImage.annotationData,
        );
        setSelectedImageData(updatedCocoData);
      } catch (error) {
        console.error('获取高清图片失败:', error);
      }
    }
  };

  // 切换到下一张图片
  const handleNextImage = async () => {
    if (imageList.length <= 1) return;

    const newIndex =
      currentImageIndex < imageList.length - 1 ? currentImageIndex + 1 : 0;
    setCurrentImageIndex(newIndex);

    const currentImage = imageList[newIndex];
    if (currentImage) {
      // 先用缩略图显示
      const cocoData = generateCOCODataFromMultimodalAnnotation(
        currentImage.imageUrl || '',
        currentImage.annotationData,
      );
      setSelectedImageData(cocoData);

      // 异步获取高清图片并更新
      try {
        const highResUrl = await getHighResImageUrl(currentImage.imageId);
        const updatedCocoData = generateCOCODataFromMultimodalAnnotation(
          highResUrl,
          currentImage.annotationData,
        );
        setSelectedImageData(updatedCocoData);
      } catch (error) {
        console.error('获取高清图片失败:', error);
      }
    }
  };

  const fetchDetail = async (page = 1, pageSize = 10) => {
    if (!datasetUuid) return;

    setLoading(true);
    try {
      const res = await getDatasetDetail({
        datasetUuid,
        page,
        pageSize,
        datasetType,
      });
      if (res.code === 0) {
        if (datasetType === 2) {
          // Multimodal dataset
          const multimodalResponse = res.data as any;
          if (multimodalResponse.list) {
            const columnNameList = multimodalResponse.list.columnNameList || [];
            const datasetDetailList =
              multimodalResponse.list.datasetDetailList || [];

            setColumnNames(columnNameList);
            setMultimodalData(datasetDetailList);
            setPagination({
              current: multimodalResponse.page || 1,
              pageSize: multimodalResponse.pageSize || 10,
              total: multimodalResponse.total || 0,
            });
            changTotalData(multimodalResponse.total || 0);
          }
        } else {
          // Single-modal dataset (datasetType === 1)
          const list = Array.isArray(res.data) ? res.data : res.data.list;
          setDataList(list || []);

          if (res.data.page) {
            setPagination({
              current: res.data.page,
              pageSize: res.data.pageSize || 10,
              total: res.data.total || 0,
            });
            changTotalData(res.data.total || 0);
          }
        }
      }
    } catch (error) {
      console.error(t('获取数据集详情失败：'), error);
    } finally {
      setLoading(false);
    }
  };

  const handleTableChange = (newPagination: any) => {
    fetchDetail(newPagination.current, newPagination.pageSize);
  };
  const handleRowClick = (
    record: DatasetDetailItem | MultimodalDatasetDetailItem,
    index: number,
  ) => {
    const recordKey = 'id' in record ? String(record.id) : String(index);
    setExpandedRowKeys((prev) =>
      prev.includes(recordKey)
        ? prev.filter((key) => key !== recordKey)
        : [...prev, recordKey],
    );
  };

  // Generate multimodal table columns
  const generateMultimodalColumns =
    (): ColumnsType<MultimodalDatasetDetailItem> => {
      const baseColumns = [
        {
          title: t('序号'),
          dataIndex: 'index',
          key: 'index',
          width: 80,
          align: 'center' as const,
          render: (_: unknown, __: unknown, index: number) => index + 1,
        },
      ];

      if (!columnNames.length) return baseColumns;

      // 严格按照 columnNameList 的顺序生成列
      const dynamicColumns = columnNames.map((columnName) => ({
        title: getColumnTitle(columnName),
        dataIndex: columnName,
        key: columnName,
        width: getColumnWidth(columnName),
        render: (
          value: any,
          record: MultimodalDatasetDetailItem,
          index: number,
        ) => {
          const isExpanded = expandedRowKeys.includes(String(index));
          return renderMultimodalCell(
            columnName,
            value,
            record,
            isExpanded,
            index,
          );
        },
      }));

      return [...baseColumns, ...dynamicColumns];
    };

  // Get column title for multimodal data
  const getColumnTitle = (columnName: string): string => {
    // 处理问答对列
    if (columnName === 'qa') {
      return t('问答对');
    }

    // 处理动态图片列
    if (columnName.startsWith('images') && !columnName.endsWith('Name')) {
      // 提取图片编号，如 images1 -> 1, images2 -> 2
      const imageNumber = columnName.replace('images', '');
      return t('图片{imageNumber}', { imageNumber });
    }

    // 处理动态图片标注列
    if (columnName.startsWith('images') && columnName.endsWith('Name')) {
      // 提取图片编号，如 images1Name -> 1, images2Name -> 2
      const imageNumber = columnName.replace('images', '').replace('Name', '');
      return t('图片{imageNumber}标注', { imageNumber });
    }

    // 其他列直接返回列名
    return columnName;
  };

  // Get column width for multimodal data
  const getColumnWidth = (columnName: string): number => {
    // 问答对列宽度较大
    if (columnName === 'qa') {
      return 300;
    }

    // 图片列宽度
    if (columnName.startsWith('images') && !columnName.endsWith('Name')) {
      return 150;
    }

    // 图片标注列宽度
    if (columnName.startsWith('images') && columnName.endsWith('Name')) {
      return 200;
    }

    // 其他列默认宽度
    return 150;
  };

  // Render multimodal cell content
  const renderMultimodalCell = (
    columnName: string,
    value: any,
    record: MultimodalDatasetDetailItem,
    isExpanded: boolean,
    _index: number,
  ) => {
    if (columnName === 'qa') {
      if (!value || !Array.isArray(value)) return '-';
      return (
        <div
          className={`${isExpanded ? 'max-h-none' : 'max-h-[200px]'} overflow-auto transition-all duration-200`}
          style={{
            fontFamily: 'monospace',
            fontSize: '12px',
            backgroundColor: '#f5f5f5',
            padding: '8px',
            borderRadius: '4px',
            whiteSpace: 'pre-wrap',
          }}>
          {JSON.stringify(value, null, 2)}
        </div>
      );
    }

    if (columnName.startsWith('images') && columnName.endsWith('Name')) {
      // Handle annotation data (images1Name, images2Name, etc.) - 显示完整JSON
      if (!value || typeof value !== 'object') return '-';
      return (
        <div
          className={`${isExpanded ? 'max-h-none' : 'max-h-[200px]'} overflow-auto transition-all duration-200`}
          style={{
            fontFamily: 'monospace',
            fontSize: '12px',
            backgroundColor: '#f5f5f5',
            padding: '8px',
            borderRadius: '4px',
            whiteSpace: 'pre-wrap',
          }}>
          {JSON.stringify(value, null, 2)}
        </div>
      );
    }

    if (columnName.startsWith('images') && !columnName.endsWith('Name')) {
      // Handle image data - use base64 data from getDatasetDetail response
      if (!value || typeof value !== 'string') return '-';

      const annotationKey = `${columnName}Name`;
      const annotationData = record[annotationKey] as MultimodalImageData;
      const imageUrl = `data:image/jpeg;base64,${value}`;

      return (
        <div
          style={{
            width: 100,
            height: 75,
            borderRadius: 4,
            cursor: 'pointer',
            border: '1px solid #d9d9d9',
            overflow: 'hidden',
          }}
          onClick={(event) =>
            handleMultimodalImageClick(imageUrl, annotationData, record, event)
          }>
          <img
            src={imageUrl}
            alt={columnName}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
            }}
          />
        </div>
      );
    }

    // Default rendering for other fields
    if (typeof value === 'string' || typeof value === 'number') {
      return (
        <div
          className={`${isExpanded ? 'max-h-none' : 'max-h-[100px]'} overflow-hidden transition-all duration-200`}>
          {value}
        </div>
      );
    }

    return (
      <div
        className={`${isExpanded ? 'max-h-none' : 'max-h-[100px]'} overflow-hidden transition-all duration-200`}>
        {JSON.stringify(value)}
      </div>
    );
  };

  // Generate single-modal table columns (existing logic)
  const generateSingleModalColumns = (): ColumnsType<DatasetDetailItem> => {
    if (!dataList.length) return [];

    // 计算每列内容的最大长度
    const columnLengths: Record<string, number> = {};
    const baseColumns = [
      {
        title: t('序号'),
        dataIndex: 'index',
        key: 'index',
        width: 80,
        align: 'center' as const,
        render: (_: unknown, __: unknown, index: number) => index + 1,
      },
    ];

    if (!dataList[0]?.datasetDetail) return baseColumns;

    // 计算每列内容的最大长度
    Object.keys(dataList[0].datasetDetail).forEach((key) => {
      columnLengths[key] = dataList.reduce((maxLength, item) => {
        const contentLength = calculateContentLength(item.datasetDetail[key]);
        return Math.max(maxLength, contentLength);
      }, key.length);
    });

    // 计算总长度和比例
    const totalLength = Object.values(columnLengths).reduce(
      (sum, len) => sum + len,
      0,
    );
    const tableWidth = window.innerWidth - 200; // 预留边距和滚动条空间

    // 定义友好的列标题映射
    const columnTitleMap: Record<string, string> = {
      messages: t('对话消息'),
    };

    const dynamicColumns = Object.keys(dataList[0].datasetDetail).map(
      (key) => ({
        title: columnTitleMap[key] || key,
        dataIndex: ['datasetDetail', key],
        key,
        width: Math.max(
          100,
          Math.floor((columnLengths[key] / totalLength) * tableWidth),
        ),
        render: (value: any, record: DatasetDetailItem) => {
          const isExpanded = expandedRowKeys.includes(record.id.toString());

          // 特殊处理新数据结构的字段
          if (key === 'qa') {
            // qa字段直接显示为格式化的JSON字符串
            return (
              <div
                className={`${isExpanded ? 'max-h-none' : 'max-h-[200px]'} overflow-auto transition-all duration-200`}
                style={{
                  fontFamily: 'monospace',
                  fontSize: '12px',
                  backgroundColor: '#f5f5f5',
                  padding: '8px',
                  borderRadius: '4px',
                  whiteSpace: 'pre-wrap',
                }}>
                {value}
              </div>
            );
          }

          if (typeof value === 'string' || typeof value === 'number') {
            return (
              <div
                className={`${isExpanded ? 'max-h-none' : 'max-h-[100px]'} overflow-hidden transition-all duration-200`}>
                {value}
              </div>
            );
          }
          return (
            <div
              className={`${isExpanded ? 'max-h-none' : 'max-h-[100px]'} overflow-hidden transition-all duration-200`}>
              {JSON.stringify(value)}
            </div>
          );
        },
      }),
    );

    return [...baseColumns, ...dynamicColumns];
  };

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!modalVisible || imageList.length <= 1) return;

      if (event.key === 'ArrowLeft') {
        event.preventDefault();
        handlePreviousImage();
      } else if (event.key === 'ArrowRight') {
        event.preventDefault();
        handleNextImage();
      }
    };

    if (modalVisible) {
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [modalVisible, currentImageIndex, imageList.length]);

  useEffect(() => {
    fetchDetail();
  }, []);

  return (
    <>
      {datasetType === 2 ? (
        <Table<MultimodalDatasetDetailItem>
          bordered
          rowKey={(_, index) => String(index)}
          columns={generateMultimodalColumns()}
          dataSource={multimodalData}
          loading={loading}
          pagination={pagination}
          onChange={handleTableChange}
          onRow={(record, index) => ({
            onClick: () => handleRowClick(record, index || 0),
            style: { cursor: 'pointer' },
          })}
          scroll={{ x: 'max-content' }}
        />
      ) : (
        <Table<DatasetDetailItem>
          bordered
          rowKey='id'
          columns={generateSingleModalColumns()}
          dataSource={dataList}
          loading={loading}
          pagination={pagination}
          onChange={handleTableChange}
          onRow={(record, index) => ({
            onClick: () => handleRowClick(record, index || 0),
            style: { cursor: 'pointer' },
          })}
          scroll={{ x: 'max-content' }}
        />
      )}

      {/* COCO标注信息Modal */}
      <Modal
        title={<span>{t('图片标注信息 (COCO格式)')}</span>}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          // 重置控制状态
          setShowBbox(true);
          setShowSegmentations(true);
        }}
        footer={
          imageList.length > 1 ? (
            <div
              className='flex items-center justify-center gap-4 py-2'
              style={{ backgroundColor: '#e6f3ff' }}>
              <Button
                type='text'
                icon={
                  <LeftOutlined
                    style={{ fontSize: '18px', color: '#1890ff' }}
                  />
                }
                onClick={handlePreviousImage}
                disabled={imageList.length <= 1}
                className='flex items-center justify-center hover:bg-blue-100 rounded-full w-10 h-10 transition-all duration-200'
                style={{ border: 'none' }}
              />
              <span
                className='px-3 py-1 rounded-full bg-blue-100 text-blue-600 font-medium'
                style={{
                  fontSize: '14px',
                  minWidth: '60px',
                  textAlign: 'center',
                }}>
                {imageList.length > 0
                  ? `${currentImageIndex + 1} / ${imageList.length}`
                  : '1 / 1'}
              </span>
              <Button
                type='text'
                icon={
                  <RightOutlined
                    style={{ fontSize: '18px', color: '#1890ff' }}
                  />
                }
                onClick={handleNextImage}
                disabled={imageList.length <= 1}
                className='flex items-center justify-center hover:bg-blue-100 rounded-full w-10 h-10 transition-all duration-200'
                style={{ border: 'none' }}
              />
            </div>
          ) : null
        }
        width={1200}
        style={{ top: 20 }}
        styles={{
          body: {
            maxHeight: '80vh',
            overflow: 'auto',
          },
        }}>
        {selectedImageData && (
          <div className='relative space-y-3'>
            {/* 图片信息栏 */}
            <div className='flex justify-between items-center px-4 py-2 bg-gray-50 rounded-md'>
              <div className='flex gap-4 text-sm'>
                <span>
                  <strong>{t('文件名')}:</strong>{' '}
                  {selectedImageData.imageInfo.file_name}
                </span>
                <span>
                  <strong>{t('尺寸')}:</strong>{' '}
                  {selectedImageData.imageInfo.width} ×{' '}
                  {selectedImageData.imageInfo.height}
                </span>
                <span>
                  <strong>ID:</strong> {selectedImageData.imageInfo.id}
                </span>
              </div>
              {imageList.length > 0 && imageList[currentImageIndex] && (
                <Tag color='blue'>
                  {getColumnTitle(imageList[currentImageIndex].columnName)}
                </Tag>
              )}
            </div>

            {/* 标注控制按钮 */}
            <div className='flex gap-2 mb-2'>
              <Button
                type={showBbox ? 'primary' : 'default'}
                size='small'
                onClick={() => setShowBbox(!showBbox)}
                className='flex items-center gap-1'>
                <span className='w-3 h-3 border-2 border-current'></span>
                {showBbox ? t('隐藏边界框') : t('显示边界框')}
              </Button>
              <Button
                type={showSegmentations ? 'primary' : 'default'}
                size='small'
                onClick={() => setShowSegmentations(!showSegmentations)}
                className='flex items-center gap-1'>
                <span className='w-3 h-3 bg-current opacity-50 rounded-sm'></span>
                {showSegmentations ? t('隐藏分割区域') : t('显示分割区域')}
              </Button>
            </div>

            {/* 图片显示区域 */}
            <div className='w-full h-[600px] border border-gray-300 rounded-md overflow-hidden relative bg-gray-50'>
              <ZoomableImage
                imageUrl={selectedImageData.imageUrl}
                alt={selectedImageData.imageInfo.file_name}
                annotations={selectedImageData.annotations}
                categories={selectedImageData.categories}
                imageInfo={selectedImageData.imageInfo}
                showBbox={showBbox}
                showSegmentations={showSegmentations}
              />
            </div>

            {/* 标注信息区域 */}
            <Card title={t('标注信息')} size='small' className='w-full'>
              <div className='space-y-3'>
                {/* 类别信息 */}
                <div>
                  <h4 className='text-sm font-medium mb-2'>{t('类别信息')}</h4>
                  <div className='flex flex-wrap gap-1'>
                    {selectedImageData.categories.map((category) => (
                      <Tag key={category.id} color='green'>
                        {category.name}
                      </Tag>
                    ))}
                  </div>
                </div>

                {/* 标注详情 */}
                <div>
                  <h4 className='text-sm font-medium mb-2'>{t('标注详情')}</h4>
                  <div className='space-y-2'>
                    {selectedImageData.annotations.map((annotation) => (
                      <div
                        key={annotation.id}
                        className='p-2 border border-gray-100 rounded text-xs space-y-1'>
                        <div>
                          <strong>ID:</strong> {annotation.id}
                        </div>
                        <div>
                          <strong>{t('类别')}:</strong>{' '}
                          {selectedImageData.categories.find(
                            (c) => c.id === annotation.category_id,
                          )?.name || 'Unknown'}
                        </div>
                        <div>
                          <strong>Bbox:</strong> [{annotation.bbox.join(', ')}]
                        </div>
                        <div>
                          <strong>{t('面积')}:</strong> {annotation.area}
                        </div>
                        {/* 分割信息 */}
                        {annotation.segmentation &&
                          annotation.segmentation.length > 0 && (
                            <div>
                              <strong>{t('分割信息')}:</strong>
                              <div className='mt-1 space-y-1'>
                                {annotation.segmentation.map(
                                  (segment, segIndex) => (
                                    <div
                                      key={segIndex}
                                      className='bg-gray-50 p-2 rounded text-xs'>
                                      <div className='font-medium text-gray-600 mb-1'>
                                        {t('多边形')} {segIndex + 1}:
                                      </div>
                                      <div className='font-mono text-gray-800 break-all'>
                                        [{segment.join(', ')}]
                                      </div>
                                      <div className='text-gray-500 mt-1'>
                                        {t('顶点数')}: {segment.length / 2}
                                      </div>
                                    </div>
                                  ),
                                )}
                              </div>
                            </div>
                          )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </Card>
          </div>
        )}
      </Modal>
    </>
  );
};

export default DataPart;
