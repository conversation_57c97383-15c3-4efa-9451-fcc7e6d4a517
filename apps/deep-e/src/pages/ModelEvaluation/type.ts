export interface TaskInfo {
  taskUUID: string;
  createdBy: number;
  createdAt: number;
  updatedAt: number;
  status: 'success' | 'failed' | 'running' | 'pending';
  finishedAt: number;
}

interface ModelConfig {
  modelPath: string;
  taskUuid: string;
  modelConfigId: number;
  taskInfo?: TaskInfo;
}

export interface ModelEvaluationConfig {
  modelA: ModelConfig;
  modelB: ModelConfig;
  testDataSetUuid: string;
  candidateSystemPrompt: string;
  candidateUserPrompt: string;
  judgeModel: ModelConfig;
  judgeSystemPrompt: string;
  judgeUserPrompt: string;
}
export interface ModelResponse {
  evalType: number;
  itemUuid: string;
  taskUuid: string; // UUID 格式标识符
  candidateSystemPrompt: string;
  candidateUserPrompt: string;
  judgeSystemPrompt: string;
  judgeUserPrompt: string;
  responseA: string; // 包含 LaTeX 格式的文本内容
  responseB: string; // 包含 LaTeX 格式的文本内容
  judgeResponse: string; // 嵌套的评审结果
}

interface ModelParams {
  modelConfigId: number;
  modelName: string;
  maxTokens: number;
  temperature: number;
  topP: number;
  topK: number;
  baseUuid?: string; // 可选字段
  knowledgeBaseName?: string; // 可选字段
  taskUuid?: string; // 可能为空字符串的情况
}

export interface EvalConfig {
  datasetItemNum: number;
  datasetName: string;
  datasetUuid: string;
  modelAConfig: ModelParams;
  modelBConfig: ModelParams;
  judgeModelConfig: ModelParams;
  candidateSystemPrompt: string;
  candidateUserPrompt: string;
  judgeSystemPrompt: string;
  judgeUserPrompt: string;
  evalType: number;
  imageFieldMapping?: { [key: string]: string }; // 可选字段，用于vision数据集的图片字段映射
  datasetType?: string; // 可选字段，用于标识数据集类型
}
