import { useEffect, useState } from 'react';
import TabCard from './components/TabCard';
import ModelInput from './components/ModelInput';
import { PromptEditor } from './components/Prompts';
import { ResultCard } from './components/ResultCard';
import { useNavigate, useLocation } from 'react-router-dom';
import { Form, InputNumber, Button, Select, Checkbox, Radio } from 'antd';
import { useAuthStore } from '@/store/features';
import { ModelList } from '@/pages/PromptWords/type';
import { getModelInfoListForSelect } from '@/pages/PromptWords/api';
import { getEvalTaskConfig } from '../../api';
import { EvalConfig } from '../../type';
import * as _ from 'lodash';
import {
  getDatasetListForSelect,
  getPromptVar,
  pairEvalByLLM,
} from '../../api';
import {
  EyeOutlined,
  SendOutlined,
  ArrowLeftOutlined,
} from '@ant-design/icons';

const RunModelEvaluation: React.FC = () => {
  const [form] = Form.useForm();
  const testDatasetUuid = Form.useWatch('testDatasetUuid', form);
  const navigate = useNavigate();
  const location = useLocation();
  const { id } = location.state || { id: 0 };

  const { token } = useAuthStore();

  const [status, setStatus] = useState('referee');
  const [datasetOptions, setDatasetOptions] = useState<any[]>([]);
  const [paramsOptions, setParamsOptions] = useState<{
    cup: { label: string; value: string }[];
    jsp: { label: string; value: string }[];
    jup: { label: string; value: string }[];
  }>({
    cup: [],
    jsp: [],
    jup: [],
  });
  const [isShowPreview, setIsShowPreview] = useState<boolean>(false);
  const [modelList, setModelList] = useState<ModelList[]>([]);

  const [datasetsIsAll, setDatasetsIsAll] = useState<boolean>(true);

  const [tableSourceMap, setTableSourceMap] = useState<
    Map<number, Map<string, string>>
  >(new Map());
  const [previewItemNum, setPreviewItemNum] = useState<number>(1);
  const [modelNames, setModelNames] = useState<{
    modelA_name: string;
    modelB_name: string;
    judge_name: string;
  }>({
    modelA_name: '',
    modelB_name: '',
    judge_name: '',
  });

  // 新增状态：数据集类型和图片字段映射
  const [datasetType, setDatasetType] = useState<'chat' | 'vision'>('chat');
  const [availableImages, setAvailableImages] = useState<number>(0);
  const [availableImageFields, setAvailableImageFields] = useState<string[]>(
    [],
  );
  const [imageFieldMapping, setImageFieldMapping] = useState<{
    [key: string]: string;
  }>({});

  const getEvalType = (status: string): number => {
    const EvalObj: { [key: string]: number } = {
      referee: 0,
      compare: 1,
      evaluate: 3,
      single: 2,
    };
    return EvalObj[status];
  };

  // 检测数据集类型的函数
  const detectDatasetType = async (datasetUuid: string) => {
    if (!datasetUuid) return;

    try {
      // 模拟API调用来检测数据集类型
      // 实际实现中，这里应该调用真实的API来获取数据集信息
      const mockDatasetInfo = await getMockDatasetInfo(datasetUuid);

      console.log(
        `[Dataset Detection] UUID: ${datasetUuid}, Type: ${mockDatasetInfo.datasetType}, Images: ${mockDatasetInfo.imageCount}, Fields: ${mockDatasetInfo.availableFields}`,
      );

      if (mockDatasetInfo.datasetType === 2) {
        setDatasetType('vision');
        setAvailableImages(mockDatasetInfo.imageCount);
        setAvailableImageFields(mockDatasetInfo.availableFields);

        // 初始化默认的字段映射
        const defaultMapping: { [key: string]: string } = {};
        for (let i = 1; i <= mockDatasetInfo.imageCount; i++) {
          const imageKey = `图片${i}`;
          // 如果有可用字段，默认选择第一个可用字段，否则留空
          defaultMapping[imageKey] =
            mockDatasetInfo.availableFields[i - 1] || '';
        }
        setImageFieldMapping(defaultMapping);

        // 设置表单字段的默认值
        form.setFieldsValue({
          imageFieldMapping: defaultMapping,
        });

        console.log(
          `[Vision Dataset] Set to vision mode with ${mockDatasetInfo.imageCount} images and fields:`,
          mockDatasetInfo.availableFields,
        );
      } else {
        setDatasetType('chat');
        setAvailableImages(0);
        setAvailableImageFields([]);
        setImageFieldMapping({});
        // 清除表单中的图片字段映射
        form.setFieldValue('imageFieldMapping', undefined);
        console.log('[Chat Dataset] Set to chat mode');
      }
    } catch (error) {
      console.error('Error detecting dataset type:', error);
      // 默认为chat类型
      setDatasetType('chat');
      setAvailableImages(0);
      setAvailableImageFields([]);
      setImageFieldMapping({});
      form.setFieldValue('imageFieldMapping', undefined);
    }
  };

  // 模拟获取数据集信息的函数
  const getMockDatasetInfo = async (datasetUuid: string) => {
    // 模拟API延迟
    await new Promise((resolve) => setTimeout(resolve, 300));

    // 模拟不同的数据集类型、图片数量和可用字段
    const mockDatasets: {
      [key: string]: {
        datasetType: number;
        imageCount: number;
        availableFields: string[];
      };
    } = {
      // 特定的测试数据集
      'chat-dataset-test-001': {
        datasetType: 1,
        imageCount: 0,
        availableFields: [],
      },
      'vision-dataset-test-001': {
        datasetType: 2,
        imageCount: 3,
        availableFields: [
          'image_url',
          'image_path',
          'image_base64',
          'image_file',
          'picture_data',
        ],
      },
      // 其他模拟vision数据集
      'vision-dataset-1': {
        datasetType: 2,
        imageCount: 2,
        availableFields: ['img1', 'img2', 'photo1', 'photo2'],
      },
      'vision-dataset-2': {
        datasetType: 2,
        imageCount: 3,
        availableFields: [
          'image_a',
          'image_b',
          'image_c',
          'picture_x',
          'picture_y',
        ],
      },
      'vision-dataset-3': {
        datasetType: 2,
        imageCount: 4,
        availableFields: [
          'field1',
          'field2',
          'field3',
          'field4',
          'field5',
          'field6',
        ],
      },
      'vision-dataset-4': {
        datasetType: 2,
        imageCount: 1,
        availableFields: ['main_image', 'primary_img', 'photo'],
      },
    };

    // 优先检查预定义的数据集
    if (mockDatasets[datasetUuid]) {
      return mockDatasets[datasetUuid];
    }

    // 如果UUID包含'vision'，则返回vision类型，否则返回chat类型
    if (datasetUuid.includes('vision')) {
      const imageCount = Math.floor(Math.random() * 4) + 1; // 1-4张图片
      const availableFields = Array.from(
        { length: imageCount + 2 },
        (_, i) => `field_${i + 1}`,
      );
      return { datasetType: 2, imageCount, availableFields };
    }

    // 默认返回chat类型
    return { datasetType: 1, imageCount: 0, availableFields: [] };
  };

  const changeParams = () => {
    if (isShowPreview) {
      setIsShowPreview(false);
      setTableSourceMap(new Map());
    }
  };

  const getDataset = async () => {
    try {
      const res = await getDatasetListForSelect();
      if (!res.code) {
        // 添加测试数据集到现有数据集列表中
        const mockTestDatasets = [
          {
            label: t('聊天测试数据集'),
            value: 'chat-dataset-test-001',
            datasetName: t('聊天测试数据集'),
            datasetUuid: 'chat-dataset-test-001',
            datasetType: 1,
          },
          {
            label: t('视觉测试数据集'),
            value: 'vision-dataset-test-001',
            datasetName: t('视觉测试数据集'),
            datasetUuid: 'vision-dataset-test-001',
            datasetType: 2,
          },
        ];

        // 将测试数据集添加到现有数据集列表的开头
        const combinedDatasets = [...mockTestDatasets, ...res.data.list];
        setDatasetOptions(combinedDatasets);
      }
    } catch (error) {
      console.error(error);
      // 如果API调用失败，至少提供测试数据集
      const fallbackTestDatasets = [
        {
          label: t('聊天测试数据集'),
          value: 'chat-dataset-test-001',
          datasetName: t('聊天测试数据集'),
          datasetUuid: 'chat-dataset-test-001',
          datasetType: 1,
        },
        {
          label: t('视觉测试数据集'),
          value: 'vision-dataset-test-001',
          datasetName: t('视觉测试数据集'),
          datasetUuid: 'vision-dataset-test-001',
          datasetType: 2,
        },
      ];
      setDatasetOptions(fallbackTestDatasets);
    }
  };

  const getModelInfoListForSelectFetch = async () => {
    try {
      const res = await getModelInfoListForSelect('chat');
      if (!res.code) {
        setModelList(res.data.list);
      }
    } catch (error) {
      console.error('Error fetching model config list:', error);
    }
  };

  const getParamsOptions = async (dataUuid: string) => {
    if (dataUuid === undefined || dataUuid === null) return;
    try {
      const res = Promise.all([
        getPromptVar(dataUuid, 'CandidateUserPrompt'),
        getPromptVar(dataUuid, 'JudgeSystemPrompt'),
        getPromptVar(dataUuid, 'JudgeUserPrompt'),
      ]);
      const [cup, jsp, jup] = await res;
      setParamsOptions({
        cup: cup.data.list,
        jsp: jsp.data.list,
        jup: jup.data.list,
      });
    } catch (error) {
      console.error(error);
    }
    return [];
  };

  const getModel = (value: string) => {
    const [groupName, modelIndex] = value.split('$');
    const model = modelList.find((item) => item.groupName === groupName);
    if (model && model.modelInfoList[Number(modelIndex)]) {
      return {
        modelConfigId: model.modelInfoList[Number(modelIndex)].modelConfigId,
        modelName: model.modelInfoList[Number(modelIndex)].modelName,
        modelPath: model.modelInfoList[Number(modelIndex)].modelPath,
        taskUuid: model.modelInfoList[Number(modelIndex)].taskUuid,
      };
    } else return null;
  };

  const getModelKey = (modelConfig: EvalConfig['modelAConfig']) => {
    let modelKey = null;
    if (modelConfig.modelConfigId !== 0) {
      for (const model of modelList) {
        const flag = model.modelInfoList.findIndex(
          (item) => item.modelConfigId === modelConfig.modelConfigId,
        );
        if (flag !== -1) {
          modelKey = `${model.groupName}$${flag}`;
        }
      }
    } else {
      for (const model of modelList) {
        const flag = model.modelInfoList.findIndex(
          (item) => item.taskUuid === modelConfig.taskUuid,
        );
        if (flag !== -1) {
          modelKey = `${model.groupName}$${flag}`;
        }
      }
    }
    return modelKey;
  };

  const onSubmit = async (
    fetch: (params: any) => Promise<any>,
    options?: any,
  ) => {
    const {
      modelA_base,
      modelA_baseUuid,
      modelB_base,
      modelB_baseUuid,
      judgeModel_base,
      judgeModel_baseUuid,
      datasetItemNum,
      ...rest
    } = await form.validateFields();

    const params = {
      ...rest,
      modelA: { ...getModel(modelA_base), baseUuid: modelA_baseUuid },
      ...(modelB_base && {
        modelB: { ...getModel(modelB_base), baseUuid: modelB_baseUuid },
      }),
      ...(judgeModel_base && {
        judgeModel: {
          ...getModel(judgeModel_base),
          baseUuid: judgeModel_baseUuid,
        },
      }),
      evalType: getEvalType(status),
      datasetItemNum: datasetsIsAll ? 0 : datasetItemNum,
      // 添加图片字段映射信息（仅对vision数据集）
      ...(datasetType === 'vision' &&
        Object.keys(imageFieldMapping).length > 0 && {
          imageFieldMapping: imageFieldMapping,
          datasetType: 'vision',
        }),
    };

    // 添加调试信息
    console.log('[Form Submission] Dataset Type:', datasetType);
    console.log('[Form Submission] Image Field Mapping:', imageFieldMapping);
    console.log('[Form Submission] Complete Params:', params);
    setModelNames({
      modelA_name: params.modelA.modelName,
      modelB_name: params.modelB?.modelName,
      judge_name: params.judgeModel?.modelName,
    });
    _.omitBy(params, _.isUndefined);
    fetch({ ...params, ...options });
  };
  const onFinish = async () => {
    await onSubmit(pairEvalByLLM);
    navigate('/modelEvaluation');
  };

  const handleStreamingPreview = async (params: any) => {
    setIsShowPreview(true);
    try {
      const response = await fetch('/api/eval/pairEvalByLLM/preview', {
        method: 'POST',
        headers: {
          'Content-Type': 'text/event-stream',
          'X-token': token,
        } as any,
        body: JSON.stringify(params),
      });

      if (!response.body) return;

      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        const chunk = decoder.decode(value);
        const lines = chunk
          .split('\n')
          .filter((line) => line.trim())
          .map((line) => line.replace(/^data:\s*/, ''));

        for (const line of lines) {
          try {
            const { text, contentType, seq } = JSON.parse(line);
            if (text === undefined) continue;
            const idx = seq - 1;
            setTableSourceMap((prev) => {
              const newMap = new Map(prev);
              if (!newMap.has(idx)) {
                newMap.set(idx, new Map());
              }
              const innerMap = new Map(newMap.get(idx));
              innerMap.set(
                contentType,
                (innerMap.get(contentType) || '') + text,
              );
              newMap.set(idx, innerMap);
              return newMap;
            });
          } catch (e) {
            console.error('Failed to parse chunk:', e);
          }
        }
      }
    } catch (error) {
      console.error('Preview failed:', error);
    }
  };

  const getEvalTaskConfigFetch = async () => {
    try {
      const res = await getEvalTaskConfig({
        taskUuid: id,
      });
      if (!res.code) {
        const detail = res.data.result as EvalConfig;

        const evalTypeMap: { [key: number]: string } = {
          0: 'referee',
          1: 'compare',
          2: 'single',
          3: 'evaluate',
        };
        setStatus(evalTypeMap[detail.evalType]);
        setDatasetsIsAll(detail.datasetItemNum === 0);

        const modelA = getModelKey(detail.modelAConfig);
        const modelB = getModelKey(detail.modelBConfig);
        const judgeModel = getModelKey(detail.judgeModelConfig);

        const params = {
          ...detail,
          testDatasetUuid: detail.datasetUuid,
          datasetItemNum: detail.datasetItemNum,
          modelA_base: modelA,
          modelB_base: modelB,
          judgeModel_base: judgeModel,
          modelA_baseUuid: detail.modelAConfig.baseUuid,
          modelB_baseUuid: detail.modelBConfig.baseUuid,
          judgeModel_baseUuid: detail.judgeModelConfig.baseUuid,
          // 如果配置中包含图片字段映射信息，则设置到表单中
          ...(detail.imageFieldMapping && {
            imageFieldMapping: detail.imageFieldMapping,
          }),
        };
        form.setFieldsValue(params);

        // 如果配置中包含图片字段映射信息，更新状态
        if (detail.imageFieldMapping) {
          setImageFieldMapping(detail.imageFieldMapping);
        }
      }
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    if (id === 0) return;
    getEvalTaskConfigFetch();
  }, [id, modelList]);

  useEffect(() => {
    getDataset();
    getModelInfoListForSelectFetch();
  }, []);

  useEffect(() => {
    getParamsOptions(testDatasetUuid);
    // 检测数据集类型
    if (testDatasetUuid) {
      detectDatasetType(testDatasetUuid);
    }
  }, [testDatasetUuid]);

  return (
    <div className='w-full p-4 flex flex-col gap-4'>
      <Button
        type='link'
        className='w-12'
        icon={<ArrowLeftOutlined />}
        onClick={() => {
          navigate('/modelEvaluation');
        }}>
        {t('返回')}
      </Button>
      <div className='w-full border border-gray-300 rounded-xl p-8 bg-white'>
        <div className='text-2xl font-bold'>{t('评估模型')}</div>
        <div className='text-gray-500 mt-1'>
          {t('选择您想要使用的评估模式')}
        </div>
        <TabCard
          value={status}
          onChange={(value) => {
            setStatus(value);
            changeParams();
          }}
          options={[
            {
              label: t('裁判模式'),
              value: 'referee',
              description: t('使用模型A、模型B和裁判模型进行评估'),
            },
            {
              label: t('对比模式'),
              value: 'compare',
              description: t('仅使用模型A和模型B进行对比'),
            },
            {
              label: t('评估模式'),
              value: 'evaluate',
              description: t('利用裁判模型评估模型A的表现'),
            },
            {
              label: t('单模型模式'),
              value: 'single',
              description: t('仅使用模型A进行输出'),
            },
          ]}
        />
      </div>

      <Form
        layout='vertical'
        form={form}
        onValuesChange={() => {
          changeParams();
        }}>
        <div className='w-full border border-gray-300 rounded-xl p-8 bg-white mb-4'>
          <div className='text-2xl font-bold mb-2'>
            <span className='text-red-500 mr-1 text-sm'>*</span>
            {t('数据集选择')}
          </div>
          <div className='text-gray-500 mb-6'>
            {t('选择要评估的基础数据集')}
          </div>
          <Form.Item
            name='testDatasetUuid'
            rules={[{ required: true, message: t('请选择数据集') }]}>
            <Select options={datasetOptions} style={{ width: 300 }} />
          </Form.Item>

          {/* 图片字段映射组件 - 仅对vision数据集显示 */}
          {datasetType === 'vision' && availableImages > 0 && (
            <div className='space-y-4 mt-6'>
              <div className='text-sm font-medium'>{t('图片字段映射')}</div>
              <div className='text-gray-500 mt-2'>
                {t('选择每个图片位置对应的数据集字段')}
              </div>
              <div className='space-y-3'>
                {Array.from({ length: availableImages }, (_, index) => {
                  const imageKey = `图片${index + 1}`;
                  return (
                    <div key={imageKey} className='flex items-center gap-4'>
                      <div className='w-16 text-sm font-medium text-gray-700'>
                        {imageKey}:
                      </div>
                      <Form.Item
                        name={['imageFieldMapping', imageKey]}
                        className='mb-0 flex-1'
                        rules={[{ required: true, message: t('请选择字段') }]}>
                        <Select
                          placeholder={t('选择字段')}
                          value={imageFieldMapping[imageKey]}
                          onChange={(value) => {
                            const newMapping = { ...imageFieldMapping };
                            newMapping[imageKey] = value;
                            setImageFieldMapping(newMapping);
                          }}
                          options={availableImageFields.map((field) => ({
                            label: field,
                            value: field,
                            // 如果字段已被其他图片选择，则禁用
                            disabled:
                              Object.values(imageFieldMapping).includes(
                                field,
                              ) && imageFieldMapping[imageKey] !== field,
                          }))}
                          style={{ minWidth: 200 }}
                        />
                      </Form.Item>
                    </div>
                  );
                })}
              </div>
              {availableImageFields.length > 0 && (
                <div className='text-xs text-gray-400 mt-2'>
                  {t('可用字段')}: {availableImageFields.join(', ')}
                </div>
              )}
            </div>
          )}

          <div className='space-y-2'>
            <div className='text-sm font-medium'>{t('数据集大小')}</div>
            <div className='text-gray-500 mt-2'>
              {t('配置评估数据集的大小')}
            </div>
            <div className='flex items-center gap-2 mb-2'>
              <Checkbox
                checked={datasetsIsAll}
                onChange={(e) => {
                  setDatasetsIsAll(e.target.checked);
                }}
              />
              <span
                className={`text-sm font-medium ${!datasetsIsAll && 'line-through'}`}>
                {t('选择全部')}
              </span>
            </div>
          </div>
          {!datasetsIsAll ? (
            <Form.Item name='datasetItemNum' initialValue={10}>
              <InputNumber />
            </Form.Item>
          ) : null}
        </div>

        <div className='w-full border border-gray-300 rounded-xl p-4 bg-white'>
          <ModelInput
            status={status as 'referee' | 'compare' | 'evaluate' | 'single'}
            modelList={modelList}
          />
        </div>
        <div>
          <PromptEditor status={status} paramsOptions={paramsOptions} />
        </div>
      </Form>
      <div className='w-full grid grid-cols-2 gap-2'>
        <div className='col-span-1 border border-gray-300 rounded-xl p-8 bg-white '>
          <div className='text-2xl font-bold'>{t('预览设置')}</div>
          <div className='text-gray-400 mt-2'>
            {t(
              '设置预览数量并查看部分结果,预览结果在这个页面下方显示,向下滑动查看',
            )}
          </div>
          <div className='flex gap-2 items-center font-bold mt-4'>
            <div>{t('预览数量:')}</div>
            <InputNumber
              value={previewItemNum}
              max={5}
              min={1}
              onChange={(value) => setPreviewItemNum(value || 1)}
            />
          </div>
          <Button
            className='w-full mt-4 text-xl'
            icon={<EyeOutlined />}
            onClick={() => {
              setTableSourceMap(new Map());
              onSubmit(handleStreamingPreview, {
                preview: true,
                previewItemNum: previewItemNum,
              });
            }}>
            {t('预览结果')}
          </Button>
        </div>
        <div className='col-span-1 border border-gray-300 rounded-xl p-8 bg-white '>
          <div className='text-2xl font-bold'>{t('提交评估')}</div>
          <div className='text-gray-400 mt-2'>
            {t('提交所有数据进行完整评估')}
          </div>
          <Button
            type='primary'
            className='w-full mt-4 text-xl'
            onClick={onFinish}
            icon={<SendOutlined />}>
            {t('提交评估')}
          </Button>
        </div>
      </div>
      {isShowPreview && (
        <div className='w-full border border-gray-300 rounded-xl p-8 bg-white'>
          <ResultCard
            evaluationMode={status}
            modelA={modelNames.modelA_name}
            modelB={modelNames.modelB_name}
            tableSourceMap={tableSourceMap}
          />
        </div>
      )}
    </div>
  );
};

export default RunModelEvaluation;
