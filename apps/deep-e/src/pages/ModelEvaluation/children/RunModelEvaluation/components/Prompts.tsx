'use client';

import { Input, Select, Button, Form, Radio, Tag } from 'antd';
import { useState, useRef } from 'react';
import KeepAlive, { AliveScope } from 'react-activation';
import { DragOutlined, CloseOutlined } from '@ant-design/icons';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  horizontalListSortingStrategy,
} from '@dnd-kit/sortable';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

const { TextArea } = Input;

// 可拖拽的图片字段组件
interface SortableImageFieldProps {
  id: string;
  field: string;
  onRemove: (field: string) => void;
}

const SortableImageField: React.FC<SortableImageFieldProps> = ({
  id,
  field,
  onRemove,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`inline-flex items-center gap-1 p-1.5 bg-blue-50 border border-blue-200 rounded-md mr-2 mb-2 ${
        isDragging ? 'shadow-lg' : ''
      }`}>
      <div
        {...attributes}
        {...listeners}
        className='cursor-move text-gray-500 hover:text-gray-700'>
        <DragOutlined className='text-xs' />
      </div>
      <Tag color='blue' className='m-0 text-xs'>
        {field}
      </Tag>
      <Button
        type='text'
        size='small'
        icon={<CloseOutlined className='text-xs' />}
        onClick={() => onRemove(field)}
        className='text-gray-400 hover:text-red-500 p-0 w-4 h-4 flex items-center justify-center'
      />
    </div>
  );
};

interface PromptEditorProps {
  status: string;
  paramsOptions: {
    cup: { label: string; value: string }[];
    jsp: { label: string; value: string }[];
    jup: { label: string; value: string }[];
  };
  availableImageFields?: string[]; // 可用的图片字段
}

export function PromptEditor({
  status,
  paramsOptions,
  availableImageFields = [],
}: PromptEditorProps) {
  const [systemPrompt, setSystemPrompt] = useState('');

  return (
    <AliveScope>
      <div className=' flex flex-col gap-4'>
        <div className='border border-gray-300 rounded-lg p-8 bg-white space-y-6 mt-4'>
          <div>
            <div className='text-2xl font-bold '>{t('模型提示词')}</div>
            <div className='text-gray-500 mt-2'>
              {t('配置模型A和模型B的提示词')}
            </div>
          </div>

          <div className='space-y-6'>
            <div className='space-y-2'>
              <div className='text-sm font-medium'>{t('系统提示词')}</div>
              <Form.Item
                name='candidateSystemPrompt'
                initialValue={
                  'You are a professional AI assistant. For each question provided by the user, you should give accurate and expert answers.'
                }>
                <TextArea
                  placeholder={t('输入系统提示词...')}
                  className='min-h-[100px]'
                  value={systemPrompt}
                  rows={5}
                  onChange={(e) => setSystemPrompt(e.target.value)}
                />
              </Form.Item>
              <p className='text-xs text-gray-500 mt-2'>
                {t('系统提示词用于设置模型的行为和角色')}
              </p>
            </div>

            <div className='space-y-2'>
              <Form.Item name='candidateUserPrompt' initialValue={'{{input}}'}>
                <CustomTextArea
                  tip={t('用户提示词是您想要模型回答的实际问题或任务')}
                  title={t('用户提示词')}
                  options={paramsOptions.cup}
                  availableImageFields={availableImageFields}
                />
              </Form.Item>
            </div>
          </div>
        </div>

        {(status === 'referee' || status === 'evaluate') && (
          <KeepAlive>
            <div className='border border-gray-300 rounded-lg p-8 bg-white'>
              <div className='space-y-6'>
                <div>
                  <div className='text-2xl font-bold'>{t('模型提示词')}</div>
                  <div className='text-gray-500 mt-2'>
                    {t('配置模型A和模型B的提示词')}
                  </div>
                </div>
                <div className='space-y-2'>
                  <Form.Item
                    name='judgeSystemPrompt'
                    initialValue={`You are a professional evaluator tasked with comparing the responses of two large language models and determining which one is better. Please assess based on accuracy, logic, clarity of expression, and completeness.Make your judgment solely based on the provided responses, without relying on any external knowledge.You must return the evaluation result in the following JSON format: {"better": "A",  // or "B","reason": "Briefly explain why you chose this model's response, in no more than 3 sentences."}`}>
                    <CustomTextArea
                      title={t('裁判系统提示词')}
                      tip={t('设置裁判模型的行为和评估标准')}
                      options={paramsOptions.jsp}
                      availableImageFields={availableImageFields}
                    />
                  </Form.Item>
                </div>

                <div className='space-y-2'>
                  <Form.Item
                    name='judgeUserPrompt'
                    initialValue={`Here is the question along with the responses from two models. Please determine which model's response is better and briefly explain the reason.
Question: {{CandidateUserPrompt}}
Response from Model A:
{{ResponseA}}
Response from Model B:
{{ResponseB}}`}>
                    <CustomTextArea
                      title={t('裁判用户提示词')}
                      tip={t('指导裁判模型如何评估模型的回答')}
                      options={paramsOptions.jup}
                      availableImageFields={availableImageFields}
                    />
                  </Form.Item>
                </div>
              </div>
            </div>
          </KeepAlive>
        )}
      </div>
    </AliveScope>
  );
}

type CustomTextAreaType = {
  onChange?: any;
  value?: string;
  tip?: string;
  title?: string;
  options: { value: any; label: string }[];
  availableImageFields?: string[]; // 可用的图片字段
  [key: string]: any;
};

const CustomTextArea: React.FC<CustomTextAreaType> = ({
  tip,
  title,
  value,
  onChange,
  options,
  availableImageFields = [],
}) => {
  const [selectedText, setSelectedText] = useState<string>(t('AI文本'));
  const [contentType, setContentType] = useState<'text' | 'image'>('text');
  const [selectedImageField, setSelectedImageField] = useState<string>('');
  const [selectedImageFields, setSelectedImageFields] = useState<string[]>([]);
  const textAreaRef = useRef<any>(null);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );

  const handleInsertText = () => {
    const textarea = textAreaRef.current?.resizableTextArea?.textArea;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const currentValue = value || '';
    const newValue = `${currentValue.slice(0, start)}${selectedText}${currentValue.slice(end)}`;
    onChange?.(newValue);

    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(
        start + selectedText.length,
        start + selectedText.length,
      );
    }, 0);
  };

  const handleAddImageField = () => {
    if (
      selectedImageField &&
      !selectedImageFields.includes(selectedImageField)
    ) {
      const newFields = [...selectedImageFields, selectedImageField];
      setSelectedImageFields(newFields);
      setSelectedImageField('');
    }
  };

  const handleRemoveImageField = (field: string) => {
    setSelectedImageFields((prev) => prev.filter((f) => f !== field));
  };

  const handleDragEnd = (event: any) => {
    const { active, over } = event;

    if (active.id !== over.id) {
      setSelectedImageFields((items) => {
        const oldIndex = items.indexOf(active.id);
        const newIndex = items.indexOf(over.id);
        return arrayMove(items, oldIndex, newIndex);
      });
    }
  };

  return (
    <div className='space-y-2'>
      <div className='text-sm font-medium'>{title}</div>

      {/* 类型选择 */}
      <div className='flex gap-4 mb-2'>
        <Radio.Group
          value={contentType}
          onChange={(e) => setContentType(e.target.value)}
          size='small'>
          <Radio value='text'>{t('文字')}</Radio>
          <Radio value='image' disabled={availableImageFields.length === 0}>
            {t('图片')}
          </Radio>
        </Radio.Group>
      </div>

      {/* 文字类型选择 */}
      {contentType === 'text' && (
        <div className='flex gap-2 mb-2'>
          <Select
            placeholder={t('请选择')}
            className='w-[200px]'
            options={options}
            onChange={(value) => {
              setSelectedText(`{{${value}}}`);
            }}
          />
          <Button onClick={handleInsertText} style={{ height: '30px' }}>
            {t('添加')}
          </Button>
        </div>
      )}

      {/* 图片类型选择 */}
      {contentType === 'image' && availableImageFields.length > 0 && (
        <div className='space-y-3'>
          <div className='flex gap-2 mb-2'>
            <Select
              placeholder={t('选择图片字段')}
              className='w-[200px]'
              value={selectedImageField}
              onChange={setSelectedImageField}
              options={availableImageFields
                .filter((field) => !selectedImageFields.includes(field))
                .map((field) => ({ label: field, value: field }))}
            />
            <Button
              onClick={handleAddImageField}
              style={{ height: '30px' }}
              disabled={
                !selectedImageField ||
                selectedImageFields.includes(selectedImageField)
              }>
              {t('添加')}
            </Button>
          </div>
        </div>
      )}

      {/* 已选择的图片字段列表 - 水平布局，独立显示 */}
      {selectedImageFields.length > 0 && (
        <div className='p-3 bg-gray-50 border border-gray-200 rounded-md'>
          <div className='text-xs text-gray-600 mb-2'>
            {t('已选择的图片字段（可拖拽调整顺序）')}:
          </div>
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}>
            <SortableContext
              items={selectedImageFields}
              strategy={horizontalListSortingStrategy}>
              <div className='flex flex-wrap'>
                {selectedImageFields.map((field) => (
                  <SortableImageField
                    key={field}
                    id={field}
                    field={field}
                    onRemove={handleRemoveImageField}
                  />
                ))}
              </div>
            </SortableContext>
          </DndContext>
        </div>
      )}

      <TextArea
        ref={textAreaRef}
        value={value}
        rows={5}
        onChange={(value) => {
          onChange?.(value.target.value);
        }}
      />
      <p className='text-xs text-gray-500 mt-2'>{tip}</p>
    </div>
  );
};
